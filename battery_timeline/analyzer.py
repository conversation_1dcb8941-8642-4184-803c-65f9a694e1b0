from __future__ import annotations
from pathlib import Path
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List
import concurrent.futures
import threading
from battery_timeline.preparator import DataPreparator
from battery_timeline.output_generator import (
    OutputGenerator,
    OutputBundle,
    LocalCSVWriter,
)

logger = logging.getLogger(__name__)


class BatteryTimelineAnalyzer:
    def __init__(
        self,
        timeline_path: Path,
        *,
        out_path: Path = Path("output/accure.csv"),
        stats_path: Path = Path("output/analyze_battery_timeline_statistics.txt"),
        detailed_path: Path = Path("output/battery_lifecycle_detailed.csv"),
        detailed_stats_path: Path | None = None,
    ) -> None:
        self.timeline_path = timeline_path
        self.type_path = "input/battery_type.csv"
        self.out_path = out_path
        self.stats_path = stats_path
        self.detailed_path = detailed_path
        self.detailed_stats_path = (
            detailed_stats_path or stats_path
        )  # Share stats file by default
        self.today = datetime.now().date()

        # Data from DataPreparator
        self.prepared_data: Dict = {}
        self.daily_stats_by_vehicle: Dict = {}
        self.vin_to_vehicle_id: Dict = {}

        # populated later
        self.timeline_df: pd.DataFrame | None = None
        self.battery_type_df: pd.DataFrame | None = None
        self.accure_data: List[Dict] = []
        self.detailed_data: List[Dict] = []  # New: for detailed lifecycle report
        self.battery_timelines = {}

        self.stats: Dict[str, int | list] = {
            "total_batteries": 0,
            "batteries_with_type": 0,
            "batteries_without_type": 0,
            "total_intervals": 0,  # New: for detailed report
            "intervals_with_km_stand": 0,  # New: for detailed report
            "errors": [],
        }

    # ─────────────── load / clean ──────────────────

    def load(self) -> None:
        logger.info("Loading timeline data and prepared data...")
        try:
            # Load timeline data from CSV file (analyzer-specific)
            logger.info(f"Loading timeline data from {self.timeline_path}")
            self.timeline_df = pd.read_csv(self.timeline_path)

            # Load battery type data
            logger.info(f"Loading battery type data from {self.type_path}")
            self.battery_type_df = pd.read_csv(self.type_path)

            # Use DataPreparator to get daily stats and VIN mappings
            logger.info("Loading prepared data using DataPreparator...")
            preparator = DataPreparator()
            self.prepared_data = preparator.load_and_prepare_data()

            # Extract the data we need for analysis
            self.daily_stats_by_vehicle = self.prepared_data["daily_stats_by_vehicle"]
            self.vin_to_vehicle_id = self.prepared_data["vin_to_vehicle_id"]

            # Optimize daily stats for fast km_stand lookups
            logger.info("Optimizing daily stats for fast km_stand calculations...")
            for vehicle_id, df in self.daily_stats_by_vehicle.items():
                df["date"] = pd.to_datetime(df["date"], errors="coerce")
                df = df.dropna(subset=["date"])
                df = df.sort_values(["date", "timestamp_start"])
                df.set_index("date", inplace=True)
                self.daily_stats_by_vehicle[vehicle_id] = df

            logger.info(
                f"Loaded {len(self.daily_stats_by_vehicle)} vehicles with daily stats"
            )
            logger.info(
                f"Loaded {len(self.vin_to_vehicle_id)} VIN to vehicle ID mappings"
            )

        except FileNotFoundError as exc:
            raise FileNotFoundError(f"Missing input file: {exc.filename}") from exc
        except Exception as exc:
            raise RuntimeError(f"Failed to load data: {exc}") from exc

    def clean(self) -> None:
        if self.timeline_df is not None:
            # Convert battery_id to string for consistent handling
            self.timeline_df["battery_id"] = self.timeline_df["battery_id"].astype(str)

            initial_count = len(self.timeline_df)
            self.timeline_df = self.timeline_df.dropna(subset=["battery_id"])

            self.timeline_df = self.timeline_df[
                ~self.timeline_df["battery_id"].str.contains(
                    "kein Tausch", case=False, na=False
                )
            ]

            # Remove records where interval_start >= interval_end (invalid/zero-duration intervals)
            before_interval_check = len(self.timeline_df)

            # Convert to datetime for comparison
            start_dates = pd.to_datetime(
                self.timeline_df["interval_start"], errors="coerce"
            )
            end_dates = pd.to_datetime(
                self.timeline_df["interval_end"], errors="coerce"
            )

            valid_intervals = (
                (start_dates < end_dates) & start_dates.notna() & end_dates.notna()
            )

            self.timeline_df = self.timeline_df[valid_intervals]

            after_interval_check = len(self.timeline_df)
            if before_interval_check != after_interval_check:
                logger.info(
                    f"Removed {before_interval_check - after_interval_check} rows with invalid/zero-duration intervals"
                )

            final_count = len(self.timeline_df)

            if initial_count != final_count:
                logger.info(
                    f"Total removed: {initial_count - final_count} rows (missing battery_id + invalid intervals)"
                )

        # Clean battery type data
        if self.battery_type_df is not None:
            self.battery_type_df["battery_id"] = self.battery_type_df[
                "battery_id"
            ].astype(str)

            self.battery_type_df = self.battery_type_df[
                ~self.battery_type_df["battery_id"].str.contains(
                    "kein Tausch", case=False, na=False
                )
            ]

            self.battery_type_df = self.battery_type_df[
                ["battery_id", "battery_type"]
            ].copy()

            # Remove duplicates
            initial_count = len(self.battery_type_df)
            self.battery_type_df = self.battery_type_df.drop_duplicates(
                subset=["battery_id"], keep="first"
            )
            final_count = len(self.battery_type_df)

            if initial_count != final_count:
                logger.info(
                    f"Removed {initial_count - final_count} duplicate battery_id entries"
                )

        logger.info(
            f"After cleaning: {len(self.timeline_df)} timeline records, {len(self.battery_type_df)} battery type records"
        )

    def _build_battery_timelines(self) -> None:
        """Pre-group and sort timelines by battery_id for fast lookup by both reports."""
        logger.info("Pre-indexing battery timelines for fast lookup...")
        if self.timeline_df is None:
            return

        for battery_id, group in self.timeline_df.groupby("battery_id"):
            # Sort by interval_start for each battery (ensures chronological order)
            sorted_group = group.sort_values("interval_start").copy()
            # Convert dates to datetime here if not already (for consistency)
            sorted_group["interval_start"] = pd.to_datetime(
                sorted_group["interval_start"], errors="coerce"
            )
            sorted_group["interval_end"] = pd.to_datetime(
                sorted_group["interval_end"], errors="coerce"
            )
            self.battery_timelines[battery_id] = sorted_group

        logger.info(
            f"Built timelines for {len(self.battery_timelines)} unique batteries"
        )

    # ─────────────── process / validate / write ──────────────────

    def process_battery_accure_report(self):
        """Process the data to create accure.csv output (battery age summary report)."""
        logger.info("Processing data for battery age summary report (accure.csv)...")

        if self.battery_type_df is None or not self.battery_timelines:
            logger.warning("No data to process")
            return

        # Get unique batteries from pre-grouped timeline data (sorted for consistency)
        unique_batteries = sorted(self.battery_timelines.keys())
        self.stats["total_batteries"] = len(unique_batteries)

        logger.info(f"Processing {len(unique_batteries)} unique batteries")

        # Create battery type lookup dictionary
        battery_type_lookup = {}
        if self.battery_type_df is not None:
            battery_type_lookup = dict(
                zip(
                    self.battery_type_df["battery_id"],
                    self.battery_type_df["battery_type"],
                )
            )

        for battery_id in unique_batteries:
            battery_type = battery_type_lookup.get(battery_id, "")

            if battery_type:
                self.stats["batteries_with_type"] += 1
            else:
                self.stats["batteries_without_type"] += 1

            # Calculate total battery age and km_stand, with confidence
            bat_info = self._calculate_battery_final_info(battery_id)

            # Create record for accure.csv
            accure_record = {
                "battery_id": battery_id,
                "battery_type": battery_type,
                "current_vin": bat_info["current_vin"],
                "battery_age": bat_info["battery_age"],
                "total_km_stand": bat_info["total_km_stand"],
                "confidence": bat_info["confidence"],
                "lifecycle_start": bat_info["lifecycle_start"],
                "lifecycle_end": bat_info["lifecycle_end"],
            }

            self.accure_data.append(accure_record)

        logger.info(f"Processed {len(self.accure_data)} battery records")
        logger.info(f"Batteries with type: {self.stats['batteries_with_type']}")
        logger.info(f"Batteries without type: {self.stats['batteries_without_type']}")

    def process_detailed_lifecycle_report(self):
        """Process detailed lifecycle report with per-interval data using multi-threading."""
        logger.info("Processing detailed lifecycle report with multi-threading...")

        if not self.battery_timelines:
            logger.warning("No timeline data to process")
            return

        unique_batteries = sorted(self.battery_timelines.keys())
        self.detailed_data = []  # Reset to empty list
        self.stats["total_intervals"] = 0
        self.stats["intervals_with_km_stand"] = 0

        # Lock for thread-safe appends and stat updates
        lock = threading.Lock()

        # Worker function to process a single battery
        def process_battery(battery_id: str):
            local_detailed_data = []  # Local list to collect records for this battery
            local_total_intervals = 0
            local_intervals_with_km_stand = 0

            timeline = self.battery_timelines[battery_id]

            # Process each interval in the battery's timeline
            for _, row in timeline.iterrows():
                vin = row["vin"]
                start = row["interval_start"]
                end = row["interval_end"]

                # Calculate duration in days
                duration = ""
                if pd.notna(start) and pd.notna(end):
                    duration = (end - start).days

                # Calculate km_stand for this interval
                km_stand, km_start, km_end, start_search_days, end_search_days = (
                    self._calculate_km_stand(vin, start, end)
                )
                if km_stand != "" and km_stand > 0:
                    local_intervals_with_km_stand += 1

                # Create detailed record
                detailed_record = {
                    "battery_id": battery_id,
                    "vin": vin,
                    "interval_start": start.date() if pd.notna(start) else "",
                    "interval_end": end.date() if pd.notna(end) else "",
                    "duration": duration,
                    "km_stand": km_stand,
                    "km_start": km_start,
                    "km_end": km_end,
                    "start_search_days": start_search_days,
                    "end_search_days": end_search_days,
                    "confidence": row.get("confidence", ""),
                    "source_event_ids": row.get("source_event_ids", ""),
                }

                local_detailed_data.append(detailed_record)
                local_total_intervals += 1

            # Return local results for aggregation
            return (
                local_detailed_data,
                local_total_intervals,
                local_intervals_with_km_stand,
            )

        # Use ThreadPoolExecutor to process batteries in parallel
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=8
        ) as executor:  # Adjust max_workers as needed
            futures = [
                executor.submit(process_battery, battery_id)
                for battery_id in unique_batteries
            ]

            for future in concurrent.futures.as_completed(futures):
                (
                    local_detailed_data,
                    local_total_intervals,
                    local_intervals_with_km_stand,
                ) = future.result()
                with lock:
                    self.detailed_data.extend(local_detailed_data)
                    self.stats["total_intervals"] += local_total_intervals
                    self.stats[
                        "intervals_with_km_stand"
                    ] += local_intervals_with_km_stand

        logger.info(
            f"Processed {self.stats['total_intervals']} intervals for detailed report"
        )
        logger.info(
            f"Intervals with km_stand data: {self.stats['intervals_with_km_stand']}"
        )

    def _calculate_km_stand(
        self, vin: str, start_date: pd.Timestamp, end_date: pd.Timestamp
    ) -> tuple[float | str, float | str, float | str, int, int]:
        """Calculate km_stand (odometer difference) for a given VIN and date range.

        Returns:
            tuple: (km_diff, start_km, end_km, start_search_days, end_search_days) where:
                   - km_diff is the difference
                   - start_km is the starting odometer reading
                   - end_km is the ending reading
                   - start_search_days is days offset to find start_km
                   - end_search_days is days offset to find end_km
                   Returns ("", "", "", 0, 0) if invalid inputs or no data found.
        """
        # Return empty if invalid inputs
        if pd.isna(start_date) or pd.isna(end_date) or start_date >= end_date:
            logger.warning(
                f"Invalid date inputs for VIN {vin}: start={start_date}, end={end_date}"
            )
            return "", "", "", 0, 0

        # Get vehicle_id from VIN
        vehicle_id = self.vin_to_vehicle_id.get(vin)
        if not vehicle_id:
            logger.warning(f"No vehicle_id found for VIN {vin}")
            return "", "", "", 0, 0

        # Get daily stats for this vehicle (already sorted and indexed by date)
        if vehicle_id not in self.daily_stats_by_vehicle:
            logger.warning(
                f"No daily stats found for vehicle_id {vehicle_id} (VIN {vin})"
            )
            return "", "", "", 0, 0

        daily_df = self.daily_stats_by_vehicle[vehicle_id]
        if daily_df.empty:
            logger.warning(f"Empty daily stats for vehicle_id {vehicle_id} (VIN {vin})")
            return "", "", "", 0, 0

        # Defensive check: ensure index is sorted (should already be sorted from load())
        if not daily_df.index.is_monotonic_increasing:
            logger.warning(
                f"Daily stats index not sorted for vehicle_id {vehicle_id}, sorting now..."
            )
            daily_df = daily_df.sort_index()
            self.daily_stats_by_vehicle[vehicle_id] = daily_df

        # Adjust search boundaries to avoid transition days
        # Start: look from day after start_date (first full day of battery usage)
        # End: look until day before end_date (last full day of battery usage)
        adjusted_start = start_date + pd.Timedelta(days=1)
        adjusted_end = end_date - pd.Timedelta(days=1)

        # Safety check: ensure we still have a valid range after adjustment
        if adjusted_start >= adjusted_end:
            # logger.warning(
            #     f"Invalid adjusted date range for VIN {vin}: adjusted_start={adjusted_start.date()}, adjusted_end={adjusted_end.date()}"
            # )
            adjusted_start = start_date
            adjusted_end = end_date
            # return "", "", "", 0, 0

        max_search_days = 30  # Phase 1: limited window

        # Phase 1: Fast search with limited window
        search_end_start = min(
            adjusted_end, adjusted_start + pd.Timedelta(days=max_search_days)
        )
        idx_start = daily_df.index.searchsorted(adjusted_start)
        start_km = None
        total_start_search_days = 0
        if idx_start < len(daily_df):
            first_date = daily_df.index[idx_start]
            if first_date <= search_end_start:
                day_data = daily_df.loc[first_date]
                min_km_start = day_data["km_start"].min()
                if not pd.isna(min_km_start):
                    start_km = min_km_start
                    total_start_search_days = (first_date - adjusted_start).days

        search_start_end = max(
            adjusted_start, adjusted_end - pd.Timedelta(days=max_search_days)
        )
        idx_end = (
            daily_df.index.searchsorted(adjusted_end + pd.Timedelta(seconds=1)) - 1
        )
        end_km = None
        total_end_search_days = 0
        if idx_end >= 0:
            last_date = daily_df.index[idx_end]
            if last_date >= search_start_end:
                day_data = daily_df.loc[last_date]
                max_km_end = day_data["km_end"].max()
                if not pd.isna(max_km_end):
                    end_km = max_km_end
                    total_end_search_days = (adjusted_end - last_date).days

        # Phase 2: Full interval search for missing data
        if start_km is None or end_km is None:
            interval_data = daily_df.loc[adjusted_start:adjusted_end]
            if not interval_data.empty:
                if start_km is None:
                    valid_start = interval_data.dropna(subset=["km_start"])
                    if not valid_start.empty:
                        first_valid_date = valid_start.index[0]
                        start_km = valid_start.loc[first_valid_date, "km_start"].min()
                        total_start_search_days = (
                            first_valid_date - adjusted_start
                        ).days

                if end_km is None:
                    valid_end = interval_data.dropna(subset=["km_end"])
                    if not valid_end.empty:
                        last_valid_date = valid_end.index[-1]
                        end_km = valid_end.loc[last_valid_date, "km_end"].max()
                        total_end_search_days = (adjusted_end - last_valid_date).days

        # Calculate difference and return all values
        if start_km is not None and end_km is not None and end_km >= start_km:
            km_diff = float(end_km - start_km)
            return (
                km_diff,
                float(start_km),
                float(end_km),
                total_start_search_days,
                total_end_search_days,
            )

        # Handle negative km_stand by searching forward for reset point
        if start_km is not None and end_km is not None and end_km < start_km:

            # Search forward from the start date to find a reset point
            reset_start_km, reset_search_days = self._find_km_reset_point(
                daily_df, adjusted_start, adjusted_end, end_km
            )

            if reset_start_km is not None:
                # Found a reset point, recalculate with new start_km
                km_diff = float(end_km - reset_start_km)
                logger.info(
                    f"Negative km_stand detected for VIN {vin}: start_km={start_km}, end_km={end_km}. Searching for reset point -> Found reset point for VIN {vin}: new start_km={reset_start_km}, new search_days={reset_search_days}, km_diff={km_diff}"
                )
                return (
                    km_diff,
                    float(reset_start_km),
                    float(end_km),
                    reset_search_days,
                    total_end_search_days,
                )
            else:
                # No reset point found, log error and return 0
                error_msg = f"Negative km_stand for VIN {vin}: start_km={start_km}, end_km={end_km}. No reset point found. Start date: {start_date.date()}, End date: {end_date.date()}"
                logger.warning(error_msg)
                self.stats["errors"].append(error_msg)
                return (
                    0.0,
                    float(start_km),
                    float(end_km),
                    total_start_search_days,
                    total_end_search_days,
                )

        # Log why we're returning empty
        if start_km is None and end_km is None:
            logger.warning(
                f"No km data found for VIN {vin} in adjusted date range {adjusted_start.date()} to {adjusted_end.date()}"
            )
        elif start_km is None:
            logger.warning(
                f"No start km found for VIN {vin} in full interval, end_km={end_km}"
            )
        elif end_km is None:
            logger.warning(
                f"No end km found for VIN {vin} in full interval, start_km={start_km}"
            )

        return (
            "",
            float(start_km) if start_km is not None else "",
            float(end_km) if end_km is not None else "",
            total_start_search_days,
            total_end_search_days,
        )

    def _find_km_reset_point(
        self,
        daily_df: pd.DataFrame,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
        current_end_km: float,
    ) -> tuple[float | None, int]:
        """Find a reset point where km_start is less than current_end_km.

        Args:
            daily_df: Daily stats DataFrame for the vehicle
            start_date: Start date to search from
            end_date: End date to search until
            current_end_km: The current end_km value to compare against

        Returns:
            tuple: (reset_start_km, search_days) where:
                   - reset_start_km is the new start_km value if reset found, None otherwise
                   - search_days is the number of days searched to find the reset point
        """
        # Search forward from start_date to find a day where km_start < current_end_km
        current_date = start_date
        search_days = 0

        while current_date <= end_date:
            search_days += 1

            # Check if we have data for this date
            if current_date in daily_df.index:
                day_data = daily_df.loc[current_date]
                min_km_start = day_data["km_start"].min()

                # If we found valid km_start data and it's less than current_end_km, we found a reset
                if not pd.isna(min_km_start) and min_km_start < current_end_km:
                    # logger.info(
                    #     f"Found km reset point at {current_date.date()}: km_start={min_km_start} < end_km={current_end_km}"
                    # )
                    return float(min_km_start), search_days

            # Move to next day
            current_date += pd.Timedelta(days=1)

        # No reset point found
        logger.info(
            f"No km reset point found in date range {start_date.date()} to {end_date.date()}"
        )
        return None, search_days

    def _calculate_battery_final_info(self, battery_id: str) -> Dict:
        """Calculate final battery information by aggregating detailed data.

        Args:
            battery_id: The battery ID to calculate info for

        Returns:
            Dict containing:
                - battery_age: chronological age in days (lifecycle_end - lifecycle_start)
                - total_km_stand: sum of km_stand across all intervals
                - confidence: average confidence
                - current_vin: VIN from last interval
                - lifecycle_start: start timestamp of first interval
                - lifecycle_end: end timestamp of last interval
        """
        # Use a copy of detailed_data to avoid modifying the original
        detailed_data_copy = self.detailed_data.copy()

        # Filter records for this battery_id
        battery_records = [
            record
            for record in detailed_data_copy
            if record["battery_id"] == battery_id
        ]

        if not battery_records:
            # Return empty values if no records found
            return {
                "battery_age": 0,
                "total_km_stand": 0,
                "confidence": "",
                "current_vin": "",
                "lifecycle_start": "",
                "lifecycle_end": "",
            }

        # Sort records by interval_start to ensure chronological order
        battery_records.sort(
            key=lambda x: (
                x["interval_start"] if x["interval_start"] != "" else pd.Timestamp.min
            )
        )

        # Calculate aggregated values
        total_km_stand = 0
        confidence_values = []

        for record in battery_records:
            # Sum km_stand
            if (
                record["km_stand"] != ""
                and pd.notna(record["km_stand"])
                and isinstance(record["km_stand"], (int, float))
                and record["km_stand"] > 0
            ):
                total_km_stand += record["km_stand"]

            # Collect confidence values for averaging
            if record["confidence"] != "" and pd.notna(record["confidence"]):
                try:
                    confidence_values.append(float(record["confidence"]))
                except (ValueError, TypeError):
                    pass  # Skip invalid confidence values

        # Calculate average confidence
        avg_confidence = ""
        if confidence_values:
            avg_confidence = sum(confidence_values) / len(confidence_values)

        # Get current_vin (VIN from last interval)
        current_vin = battery_records[-1]["vin"] if battery_records else ""

        # Get lifecycle_start (start of first interval)
        lifecycle_start = (
            battery_records[0]["interval_start"] if battery_records else ""
        )

        # Get lifecycle_end (end of last interval)
        lifecycle_end = battery_records[-1]["interval_end"] if battery_records else ""

        # Calculate battery age as time from first start to today
        battery_age = 0
        if lifecycle_start != "" and pd.notna(lifecycle_start):
            try:
                # Convert to pandas timestamps if they aren't already
                start_ts = (
                    pd.to_datetime(lifecycle_start)
                    if not isinstance(lifecycle_start, pd.Timestamp)
                    else lifecycle_start
                )
                today_ts = pd.Timestamp.now().normalize()

                # Calculate age in days from start to today
                battery_age = (today_ts - start_ts).days
            except (ValueError, TypeError):
                battery_age = 0  # Fallback if timestamp conversion fails

        return {
            "battery_age": battery_age,
            "total_km_stand": total_km_stand,
            "confidence": avg_confidence,
            "current_vin": current_vin,
            "lifecycle_start": lifecycle_start,
            "lifecycle_end": lifecycle_end,
        }

    def generate_outputs(self):
        """Generate both the battery age summary report and detailed lifecycle report."""
        logger.info("Generating output files...")

        # Build enhanced stats with processing metadata
        enhanced_stats = self._build_enhanced_stats()

        # Generate battery age summary report (accure.csv)
        age_csv_file, age_stats_file = None, None
        if self.accure_data:
            logger.info("Generating battery summary report (accure.csv)...")
            age_bundle = OutputBundle(
                records=self.accure_data,
                stats=enhanced_stats,
                csv_path=Path(self.out_path),
                stats_path=Path(self.stats_path),
            )

            writer = LocalCSVWriter()
            output_generator = OutputGenerator(writer)
            age_csv_file, age_stats_file = output_generator(age_bundle)

            logger.info(
                f"Saved {len(self.accure_data)} battery age records to {age_csv_file}"
            )
        else:
            logger.warning("No battery age data to output")

        # Generate detailed lifecycle report
        detailed_csv_file, detailed_stats_file = None, None
        if self.detailed_data:
            logger.info("Generating detailed lifecycle report...")
            detailed_bundle = OutputBundle(
                records=self.detailed_data,
                stats=enhanced_stats,
                csv_path=Path(self.detailed_path),
                stats_path=Path(self.detailed_stats_path),
            )

            writer = LocalCSVWriter()
            output_generator = OutputGenerator(writer)
            detailed_csv_file, detailed_stats_file = output_generator(detailed_bundle)

            logger.info(
                f"Saved {len(self.detailed_data)} detailed interval records to {detailed_csv_file}"
            )
        else:
            logger.warning("No detailed lifecycle data to output")

        logger.info(f"Statistics saved to {age_stats_file or detailed_stats_file}")

        return (
            str(age_csv_file) if age_csv_file else None,
            str(detailed_csv_file) if detailed_csv_file else None,
            (
                str(age_stats_file or detailed_stats_file)
                if (age_stats_file or detailed_stats_file)
                else None
            ),
        )

    def _build_enhanced_stats(self):
        """Build enhanced statistics for analyzer output."""
        enhanced_stats = self.stats.copy()
        enhanced_stats["processing_date"] = str(datetime.now())
        enhanced_stats["battery_age_output_file"] = str(self.out_path)
        enhanced_stats["detailed_lifecycle_output_file"] = str(self.detailed_path)
        enhanced_stats["total_battery_age_records"] = len(self.accure_data)
        enhanced_stats["total_detailed_records"] = len(self.detailed_data)

        return enhanced_stats

    def run(self) -> tuple[str | None, str | None, str | None]:
        """Run the complete analysis pipeline for both reports."""
        self.load()
        self.clean()
        self._build_battery_timelines()
        self.process_detailed_lifecycle_report()
        self.process_battery_accure_report()
        age_csv, detailed_csv, stats_file = self.generate_outputs()
        return age_csv, detailed_csv, stats_file
