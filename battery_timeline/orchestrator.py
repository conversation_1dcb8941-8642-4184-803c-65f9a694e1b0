import logging
from pathlib import Path
from datetime import datetime

from battery_timeline.preparator import DataPreparator
from battery_timeline.processing import TimelineTransformer
from battery_timeline.validator import TimelineValidator
from battery_timeline.output_generator import (
    OutputGenerator,
    OutputBundle,
    LocalCSVWriter,
)

logger = logging.getLogger(__name__)


class BatteryTimelineGenerator:
    """
    Orchestrates the entire battery timeline analysis process.
    Coordinates DataPreparator, TimelineTransformer, TimelineValidator, and OutputGenerator.
    """

    def __init__(self):
        self.today = datetime.now().date()

        # Results from processing
        self.battery_timelines = []  # Final timeline results
        self.conflicts = []  # Conflicts found during processing

        self.stats = {
            "total_batteries": 0,
            "total_vehicles": 0,
            "working_only_vehicles": 0,
            "errors": [],
        }

    def run(self):
        """Run the battery timeline analysis process."""
        logger.info("Starting battery timeline analysis...")

        try:
            # Use DataPreparator to load and prepare all data
            logger.info("Preparing data using DataPreparator...")
            preparator = DataPreparator()
            prepared_data = preparator.load_and_prepare_data()

            # Update stats from prepared data
            self.stats = prepared_data["stats"]

            # Use TimelineTransformer to transform prepared data into timelines
            logger.info("Transforming data using TimelineTransformer...")
            transformer = TimelineTransformer(prepared_data)
            self.battery_timelines, self.conflicts = transformer.transform()

            # Validate timelines using TimelineValidator
            logger.info("Validating timelines using TimelineValidator...")
            validator = TimelineValidator()
            validation_result = validator.validate(self.battery_timelines, prepared_data["vehicle_info"])

            # Update stats with validation results
            self.stats.update(
                {
                    "validation_valid": validation_result["valid"],
                    "validation_errors": len(validation_result["errors"]),
                    "validation_successes": len(validation_result["successes"]),
                    "validation_violations": len(validation_result["violations"]),
                }
            )

            # Generate outputs using OutputGenerator
            logger.info("Generating outputs using OutputGenerator...")

            # Build enhanced stats with dual-battery analysis
            enhanced_stats = self._build_enhanced_stats(
                self.battery_timelines, self.stats
            )

            bundle = OutputBundle(
                records=self.battery_timelines,  # ← already list of dicts
                stats=enhanced_stats,
                csv_path=Path("output/battery_lifecycle_timelines.csv"),
                stats_path=Path("output/battery_timeline_statistics.json"),
            )
            writer = LocalCSVWriter()
            output_generator = OutputGenerator(writer)
            csv_file, stats_file = output_generator(bundle)

            logger.info("Battery timeline analysis completed successfully!")
            if csv_file:
                logger.info(f"Timeline data saved to: {csv_file}")
            logger.info(f"Statistics saved to: {stats_file}")

            logger.info(f"Timeline intervals: {len(self.battery_timelines)}")
            logger.info(
                f"Total dual battery vehicles: {enhanced_stats.get('dual_battery_vehicles', 0)}"
            )

            return csv_file, stats_file

        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise

    def _build_enhanced_stats(self, timelines, base_stats):
        """Build enhanced statistics including dual-battery analysis."""
        from collections import defaultdict

        enhanced_stats = base_stats.copy()
        enhanced_stats["processing_date"] = str(datetime.now())

        if timelines:
            # Timeline-specific stats
            enhanced_stats["total_timeline_intervals"] = len(timelines)
            active_intervals = len([i for i in timelines if i["interval_end"] is None])
            enhanced_stats["currently_active_intervals"] = active_intervals
            enhanced_stats["completed_intervals"] = len(timelines) - active_intervals

            # Dual-battery analysis
            dual_battery_vins = []
            by_vin = defaultdict(list)
            for iv in timelines:
                by_vin[iv["vin"]].append(iv)

            for vin, intervals in by_vin.items():
                concurrent = len([iv for iv in intervals if iv["interval_end"] == self.today])
                if concurrent == 2:
                    dual_battery_vins.append(vin)
                elif concurrent > 2:
                    logger.warning(f"VIN {vin} has {concurrent} active batteries")

            enhanced_stats["dual_battery_vehicles"] = len(dual_battery_vins)
            enhanced_stats["dual_battery_vins"] = dual_battery_vins[
                :10
            ]  # Sample for JSON
            if len(dual_battery_vins) > 10:
                enhanced_stats["dual_battery_vins_truncated"] = True

            # Log dual-battery info
            logger.info(f"Vehicles with 2 active batteries: {len(dual_battery_vins)}")

        return enhanced_stats
