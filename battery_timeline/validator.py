from datetime import datetime, date, timedelta
from typing import Dict, List, Any
from collections import defaultdict
from battery_timeline.models import BatteryInterval

import logging

logger = logging.getLogger(__name__)


class TimelineValidator:
    def __init__(self):
        self.today = datetime.now().date()

    def validate(
        self, timelines: List[BatteryInterval], vehicle_info
    ) -> Dict[str, Any]:
        """
        Validate the generated timelines for correctness.

        Args:
            timelines: List of BatteryInterval dicts to validate

        Returns:
            Dict containing validation results:
            {
                'valid': bool,
                'errors': List[str],
                'successes': List[str],
                'violations': List[Dict]
            }
        """
        logger.info("Validating timelines...")

        # Initialize result structure
        result = {"valid": True, "errors": [], "successes": [], "violations": []}

        # Run battery exclusivity validation
        exclusivity_result = self._validate_battery_exclusivity(timelines)
        result["violations"].extend(exclusivity_result["violations"])
        result["errors"].extend(exclusivity_result["errors"])

        # Run erstzulassung correctness validation
        erstzulassung_result = self._validate_erstzulassung_correctness(timelines)
        result["errors"].extend(erstzulassung_result["errors"])
        result["successes"].extend(erstzulassung_result["successes"])

        # Run vehicle last activity validation
        last_activity_result = self._validate_vehicle_last_activity(
            timelines, vehicle_info
        )
        result["errors"].extend(last_activity_result["errors"])
        result["successes"].extend(last_activity_result["successes"])

        # Overall validation status
        result["valid"] = len(result["errors"]) == 0 and len(result["violations"]) == 0

        # Log summary
        logger.info(f"Timeline validation completed:")
        logger.info(f"  - Valid: {result['valid']}")
        logger.info(f"  - Errors: {len(result['errors'])}")
        logger.info(f"  - Successes: {len(result['successes'])}")
        logger.info(f"  - Violations: {len(result['violations'])}")

        return result

    def _validate_erstzulassung_correctness(
        self, battery_timelines: List[BatteryInterval]
    ) -> Dict[str, Any]:
        """
        Validate that vehicles with erstzulassung_candidate intervals
        actually have their first timeline interval starting with the expected VIN.
        """
        logger.info("Validating erstzulassung correctness...")

        # Only process timelines with valid start and end dates
        valid_timelines = [
            iv
            for iv in battery_timelines
            if iv["interval_start"] is not None
            and iv["interval_end"] is not None
            and iv["interval_start"] < iv["interval_end"]
        ]

        # Step 1: Find all VINs that have erstzulassung_candidate intervals
        erstzulassung_candidate_vins = set()
        erstzulassung_intervals = []
        for interval in valid_timelines:
            if interval.get("erstzulassung_candidate", False):
                erstzulassung_candidate_vins.add(interval["vin"])
                erstzulassung_intervals.append(interval)

        if not erstzulassung_intervals:
            logger.info("No erstzulassung_candidate intervals found to validate")
            return {"errors": [], "successes": []}

        logger.info(
            f"Found {len(erstzulassung_candidate_vins)} VINs with erstzulassung_candidate intervals"
        )

        # Step 2: Group timelines by VIN and sort chronologically
        timelines_by_vin = defaultdict(list)
        for interval in valid_timelines:
            if interval["interval_start"] is None or interval["interval_end"] is None:
                continue
            timelines_by_vin[interval["vin"]].append(interval)

        # Sort each VIN's intervals chronologically
        for vin in timelines_by_vin:
            timelines_by_vin[vin].sort(
                key=lambda iv: (
                    iv["interval_start"] or iv["interval_end"] or date(1900, 1, 1),
                    iv["interval_end"] or self.today,
                )
            )

        # Step 3: Build battery usage index for conflict detection
        battery_usage = defaultdict(list)
        for interval in valid_timelines:
            battery_usage[interval["battery_id"]].append(interval)

        # Step 4: Validate each erstzulassung_candidate interval
        validation_errors = []
        validation_successes = []

        for interval in erstzulassung_intervals:
            vin = interval["vin"]
            battery_id = interval["battery_id"]
            start_date = interval["interval_start"]
            end_date = interval["interval_end"]

            # Check if this is the first interval for this VIN
            if vin not in timelines_by_vin or not timelines_by_vin[vin]:
                validation_errors.append(
                    f"VIN {vin} has erstzulassung_candidate but no timeline intervals"
                )
                continue

            first_interval = timelines_by_vin[vin][0]

            # Check if the first interval is indeed for this VIN
            if first_interval["vin"] != vin:
                validation_errors.append(
                    f"VIN {vin} erstzulassung validation failed: "
                    f"first interval belongs to VIN {first_interval['vin']}, not {vin}"
                )
                continue

            if start_date is None:
                validation_errors.append(
                    f"VIN {vin} erstzulassung validation failed: "
                    f"first interval has no start date despite erstzulassung_candidate processing"
                )
                continue

            # Check if battery is used elsewhere during erstzulassung period
            battery_conflicts = []
            for other_interval in battery_usage[battery_id]:
                if other_interval["vin"] == vin:
                    continue  # Skip same VIN

                other_start = other_interval["interval_start"]
                other_end = other_interval["interval_end"] or self.today
                period_end = end_date or date.max
                if other_start is None:
                    continue

                if start_date < other_end and other_start < period_end:
                    battery_conflicts.append(other_interval["vin"])

            if battery_conflicts:
                validation_errors.append(
                    f"VIN {vin} erstzulassung validation failed: "
                    f"battery {battery_id} also used in VINs {battery_conflicts} during erstzulassung period"
                )
            else:
                validation_successes.append(
                    f"VIN {vin} erstzulassung validation passed: "
                    f"battery {battery_id} exclusively used, starts on {start_date}"
                )

        # Step 5: Report validation results
        logger.info(f"Erstzulassung validation completed:")
        logger.info(f"  - {len(validation_successes)} VINs passed validation")
        logger.info(f"  - {len(validation_errors)} VINs failed validation")

        if validation_errors:
            logger.warning("Validation errors found:")
            for error in validation_errors:
                logger.warning(f"  ✗ {error}")

        return {"errors": validation_errors, "successes": validation_successes}

    def _validate_battery_exclusivity(
        self, timelines: List[BatteryInterval]
    ) -> Dict[str, Any]:
        """
        Validate that each battery is only used in one vehicle at any given time.
        Check for overlapping intervals for the same battery across different VINs.
        """
        logger.info("Validating battery exclusivity...")

        # Group intervals by battery_id
        battery_usage = defaultdict(list)
        for interval in timelines:
            start = interval.get("interval_start")
            end = interval.get("interval_end")
            if start is None or end is None or start >= end:
                continue
            battery_usage[interval["battery_id"]].append(interval)

        exclusivity_violations = []
        total_batteries_checked = 0

        for battery_id, intervals in battery_usage.items():
            total_batteries_checked += 1
            if len(intervals) <= 1:
                continue  # Single interval, no conflicts possible

            # Create events for sweep line
            events = []
            for interval in intervals:
                events.append((interval["interval_start"], "start", interval))
                events.append((interval["interval_end"], "end", interval))

            # Sort events: by time ascending, then process 'end' before 'start' for same time
            events.sort(key=lambda e: (e[0], 0 if e[1] == "end" else 1))

            # Active intervals grouped by VIN: vin -> list of active intervals
            active_vins = defaultdict(list)

            battery_violations = []

            for _, event_type, interval in events:
                vin = interval["vin"]
                if event_type == "start":
                    # Check for overlaps with active intervals from other VINs
                    for other_vin, active_intervals in active_vins.items():
                        if other_vin == vin:
                            continue
                        for active_interval in active_intervals:
                            # Prepare violation with sorted VINs for consistency
                            vin1, vin2 = sorted([vin, other_vin])
                            if vin1 == interval["vin"]:
                                start1, end1 = (
                                    interval["interval_start"],
                                    interval["interval_end"],
                                )
                                start2, end2 = (
                                    active_interval["interval_start"],
                                    active_interval["interval_end"],
                                )
                            else:
                                start1, end1 = (
                                    active_interval["interval_start"],
                                    active_interval["interval_end"],
                                )
                                start2, end2 = (
                                    interval["interval_start"],
                                    interval["interval_end"],
                                )
                            battery_violations.append(
                                {
                                    "battery_id": battery_id,
                                    "vin1": vin1,
                                    "vin1_start": start1,
                                    "vin1_end": end1,
                                    "vin2": vin2,
                                    "vin2_start": start2,
                                    "vin2_end": end2,
                                }
                            )
                    # Add this interval to active
                    active_vins[vin].append(interval)
                elif event_type == "end":
                    # Remove this interval from active
                    if interval in active_vins[vin]:
                        active_vins[vin].remove(interval)

            # Add to total violations
            exclusivity_violations.extend(battery_violations)

        # Report results
        logger.info(f"Battery exclusivity validation completed:")
        logger.info(f"  - {total_batteries_checked} batteries checked")
        logger.info(f"  - {len(exclusivity_violations)} exclusivity violations found")

        if exclusivity_violations:
            logger.warning("Battery exclusivity violations found:")
            for violation in exclusivity_violations[:10]:  # Show first 10
                logger.warning(
                    f"  ✗ Battery {violation['battery_id']}: "
                    f"VIN {violation['vin1']} ({violation['vin1_start']} to {violation['vin1_end']}) "
                    f"overlaps with VIN {violation['vin2']} ({violation['vin2_start']} to {violation['vin2_end']})"
                )
            if len(exclusivity_violations) > 10:
                logger.warning(
                    f"  ... and {len(exclusivity_violations) - 10} more violations"
                )

        return {"violations": exclusivity_violations, "errors": []}

    def _validate_vehicle_last_activity(
        self, timelines: List[BatteryInterval], vehicle_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate that for every unique vehicle in timelines, there's a battery installed
        near the vehicle's last activity date (within 30 days threshold).

        Args:
            timelines: List of BatteryInterval dicts to validate
            vehicle_info: Dict mapping VIN to vehicle information including last_active_date

        Returns:
            Dict containing validation results with errors and successes
        """
        logger.info("Validating vehicle last activity coverage...")

        # Get all unique VINs from timelines
        timeline_vins = set()
        for interval in timelines:
            if interval.get("vin"):
                timeline_vins.add(interval["vin"])

        logger.info(f"Found {len(timeline_vins)} unique vehicles in timelines")

        # Group timelines by VIN for efficient lookup
        timelines_by_vin = defaultdict(list)
        for interval in timelines:
            vin = interval.get("vin")
            if vin and interval.get("interval_start") and interval.get("interval_end"):
                timelines_by_vin[vin].append(interval)

        # Sort intervals by end date for each VIN (latest first)
        for vin in timelines_by_vin:
            timelines_by_vin[vin].sort(
                key=lambda iv: iv["interval_end"] or date.min, reverse=True
            )

        validation_errors = []
        validation_successes = []
        threshold_days = 30

        # Check each vehicle
        for vin in timeline_vins:
            vehicle_data = vehicle_info.get(vin)
            if not vehicle_data:
                validation_errors.append(
                    f"VIN {vin} found in timelines but missing from vehicle_info"
                )
                continue

            last_active_date = vehicle_data.get("last_active_date")
            if not last_active_date:
                # Vehicle has no activity data, skip validation
                continue

            # Find the battery interval that was active closest to the last activity date
            vehicle_intervals = timelines_by_vin.get(vin, [])
            if not vehicle_intervals:
                validation_errors.append(
                    f"VIN {vin} has last activity on {last_active_date} but no battery intervals"
                )
                continue

            # Find the interval with the latest end date that's closest to last activity
            closest_interval = None
            min_gap_days = float("inf")

            for interval in vehicle_intervals:
                interval_end = interval["interval_end"]
                if interval_end:
                    gap_days = abs((last_active_date - interval_end).days)
                    if gap_days < min_gap_days:
                        min_gap_days = gap_days
                        closest_interval = interval

            if closest_interval is None:
                validation_errors.append(
                    f"VIN {vin} has last activity on {last_active_date} but no valid battery intervals with end dates"
                )
                continue

            # Check if the closest interval is within threshold
            if min_gap_days <= threshold_days:
                validation_successes.append(
                    f"VIN {vin} last activity validation passed: "
                    f"last active {last_active_date}, battery {closest_interval['battery_id']} "
                    f"ended {closest_interval['interval_end']} ({min_gap_days} days gap)"
                )
            else:
                validation_errors.append(
                    f"VIN {vin} last activity validation failed: "
                    f"last active {last_active_date}, closest battery {closest_interval['battery_id']} "
                    f"ended {closest_interval['interval_end']} ({min_gap_days} days gap > {threshold_days} threshold)"
                )

        # Log summary
        logger.info(f"Vehicle last activity validation completed:")
        logger.info(f"  - {len(validation_successes)} vehicles passed validation")
        logger.info(f"  - {len(validation_errors)} vehicles failed validation")

        if validation_errors:
            logger.warning("Last activity validation errors found:")
            for error in validation_errors[:10]:  # Show first 10
                logger.warning(f"  ✗ {error}")
            if len(validation_errors) > 10:
                logger.warning(f"  ... and {len(validation_errors) - 10} more errors")

        return {"errors": validation_errors, "successes": validation_successes}
