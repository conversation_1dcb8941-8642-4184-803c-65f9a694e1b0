#!/usr/bin/env python3
"""
Simple test script to verify the vehicle last activity validation works correctly.
"""

from datetime import date, timedelta
from battery_timeline.validator import TimelineV<PERSON>da<PERSON>

def test_vehicle_last_activity_validation():
    """Test the vehicle last activity validation logic."""
    
    # Create test data
    today = date.today()
    
    # Test timelines with different scenarios
    timelines = [
        # Vehicle 1: Battery ends within threshold (should pass)
        {
            "battery_id": "BAT001",
            "vin": "VIN001",
            "interval_start": today - timedelta(days=100),
            "interval_end": today - timedelta(days=25),  # 25 days before today
            "interval_type": "active",
            "lifecycle_stage": "in_use",
            "source_event_ids": [1],
            "confidence": 1.0,
            "notes": "Test battery 1"
        },
        # Vehicle 2: Battery ends outside threshold (should fail)
        {
            "battery_id": "BAT002", 
            "vin": "VIN002",
            "interval_start": today - timedelta(days=200),
            "interval_end": today - timedelta(days=50),  # 50 days before today
            "interval_type": "active",
            "lifecycle_stage": "in_use", 
            "source_event_ids": [2],
            "confidence": 1.0,
            "notes": "Test battery 2"
        },
        # Vehicle 3: Multiple intervals, latest one within threshold (should pass)
        {
            "battery_id": "BAT003A",
            "vin": "VIN003", 
            "interval_start": today - timedelta(days=300),
            "interval_end": today - timedelta(days=200),
            "interval_type": "active",
            "lifecycle_stage": "in_use",
            "source_event_ids": [3],
            "confidence": 1.0,
            "notes": "Test battery 3A (older)"
        },
        {
            "battery_id": "BAT003B",
            "vin": "VIN003",
            "interval_start": today - timedelta(days=200), 
            "interval_end": today - timedelta(days=20),  # 20 days before today
            "interval_type": "active",
            "lifecycle_stage": "in_use",
            "source_event_ids": [4],
            "confidence": 1.0,
            "notes": "Test battery 3B (newer)"
        }
    ]
    
    # Test vehicle info
    vehicle_info = {
        "VIN001": {
            "vin": "VIN001",
            "last_active_date": today - timedelta(days=20),  # 20 days ago
            "erstzulassung": today - timedelta(days=365),
            "first_active_date": today - timedelta(days=365)
        },
        "VIN002": {
            "vin": "VIN002", 
            "last_active_date": today - timedelta(days=10),  # 10 days ago
            "erstzulassung": today - timedelta(days=365),
            "first_active_date": today - timedelta(days=365)
        },
        "VIN003": {
            "vin": "VIN003",
            "last_active_date": today - timedelta(days=15),  # 15 days ago
            "erstzulassung": today - timedelta(days=365),
            "first_active_date": today - timedelta(days=365)
        },
        "VIN004": {
            "vin": "VIN004",
            "last_active_date": None,  # No activity data
            "erstzulassung": today - timedelta(days=365),
            "first_active_date": today - timedelta(days=365)
        }
    }
    
    # Run validation
    validator = TimelineValidator()
    result = validator._validate_vehicle_last_activity(timelines, vehicle_info)
    
    print("=== Vehicle Last Activity Validation Test Results ===")
    print(f"Errors: {len(result['errors'])}")
    print(f"Successes: {len(result['successes'])}")
    print()
    
    print("Errors:")
    for error in result['errors']:
        print(f"  ✗ {error}")
    print()
    
    print("Successes:")
    for success in result['successes']:
        print(f"  ✓ {success}")
    print()
    
    # Expected results:
    # VIN001: Should pass (battery ends 25 days ago, last active 20 days ago, gap = 5 days <= 30)
    # VIN002: Should fail (battery ends 50 days ago, last active 10 days ago, gap = 40 days > 30)  
    # VIN003: Should pass (latest battery ends 20 days ago, last active 15 days ago, gap = 5 days <= 30)
    # VIN004: Should be skipped (no last_active_date)
    
    expected_successes = 2  # VIN001, VIN003
    expected_errors = 1     # VIN002
    
    if len(result['successes']) == expected_successes and len(result['errors']) == expected_errors:
        print("✅ Test PASSED - Results match expectations!")
    else:
        print(f"❌ Test FAILED - Expected {expected_successes} successes and {expected_errors} errors")
        print(f"   Got {len(result['successes'])} successes and {len(result['errors'])} errors")

if __name__ == "__main__":
    test_vehicle_last_activity_validation()
